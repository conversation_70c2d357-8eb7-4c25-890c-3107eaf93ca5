import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  server: {
    proxy: {
      // 代理所有 /api 开头的请求到后端 API Gateway
      '/api': {
        target: 'http://127.0.0.1:51287', // 这里会在后续根据 minikube service 的实际 URL 进行更新
        changeOrigin: true,
        secure: false,
      },
      // 代理认证相关的请求
      '/auth': {
        target: 'http://127.0.0.1:51287',
        changeOrigin: true,
        secure: false,
      }
    }
  }
})
