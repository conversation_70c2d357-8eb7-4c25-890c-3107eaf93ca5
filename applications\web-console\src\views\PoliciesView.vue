<template>
  <div class="policies-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>政策管理</span>
          <el-button type="primary">
            <el-icon><Plus /></el-icon>
            添加政策
          </el-button>
        </div>
      </template>
      
      <div class="content-placeholder">
        <el-empty description="政策管理功能开发中..." />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { Plus } from '@element-plus/icons-vue'
</script>

<style scoped>
.policies-container {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.content-placeholder {
  padding: 60px 0;
  text-align: center;
}
</style>
