<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { Plus, Search, User, Document, Files, CircleCheck } from '@element-plus/icons-vue'

const authStore = useAuthStore()

// 系统统计数据
const stats = ref({
  totalUsers: 1234,
  totalTasks: 567,
  totalPolicies: 89,
  systemStatus: '正常'
})

// 最近活动数据
const recentActivities = ref([
  { id: 1, action: '用户登录', user: 'admin', time: '2025-01-17 10:30:00' },
  { id: 2, action: '创建任务', user: 'user1', time: '2025-01-17 10:25:00' },
  { id: 3, action: '更新政策', user: 'admin', time: '2025-01-17 10:20:00' },
  { id: 4, action: '查询知识库', user: 'user2', time: '2025-01-17 10:15:00' },
])

onMounted(() => {
  console.log('首页加载完成，当前用户:', authStore.username)
})
</script>

<template>
  <div class="home-container">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <el-card class="welcome-card">
        <div class="welcome-content">
          <h2>欢迎回来，{{ authStore.username }}！</h2>
          <p>今天是 {{ new Date().toLocaleDateString('zh-CN') }}，祝您工作愉快！</p>
        </div>
        <div class="welcome-actions">
          <el-button type="primary" size="large">
            <el-icon><Plus /></el-icon>
            创建新任务
          </el-button>
          <el-button size="large">
            <el-icon><Search /></el-icon>
            查询数据
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon user-icon">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.totalUsers }}</div>
                <div class="stat-label">总用户数</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="12" :sm="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon task-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.totalTasks }}</div>
                <div class="stat-label">总任务数</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="12" :sm="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon policy-icon">
                <el-icon><Files /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.totalPolicies }}</div>
                <div class="stat-label">政策数量</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="12" :sm="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon status-icon">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.systemStatus }}</div>
                <div class="stat-label">系统状态</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 最近活动 -->
    <div class="activity-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>最近活动</span>
            <el-button type="text">查看全部</el-button>
          </div>
        </template>

        <el-table :data="recentActivities" style="width: 100%">
          <el-table-column prop="action" label="操作" width="120" />
          <el-table-column prop="user" label="用户" width="100" />
          <el-table-column prop="time" label="时间" />
          <el-table-column label="操作" width="100">
            <template #default>
              <el-button type="text" size="small">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<style scoped>
.home-container {
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section {
  margin-bottom: 24px;
}

.welcome-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.welcome-card :deep(.el-card__body) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32px;
}

.welcome-content h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.welcome-content p {
  margin: 0;
  opacity: 0.9;
  font-size: 14px;
}

.welcome-actions {
  display: flex;
  gap: 12px;
}

.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  height: 100px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.user-icon {
  background: #409eff;
}

.task-icon {
  background: #67c23a;
}

.policy-icon {
  background: #e6a23c;
}

.status-icon {
  background: #f56c6c;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.activity-section {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-card :deep(.el-card__body) {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .welcome-actions {
    justify-content: center;
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 8px;
  }
}
</style>
